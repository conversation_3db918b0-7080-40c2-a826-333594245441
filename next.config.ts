import type { NextConfig } from "next";

// Dynamic import for bundle analyzer
const withBundleAnalyzer =
  process.env.ANALYZE === "true"
    ? require("@next/bundle-analyzer")({ enabled: true })
    : (config: NextConfig) => config;

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Production output optimization
  output: "standalone",

  // Image optimization with Next.js 15 improvements
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "seoanalyser.com.au",
      },
      {
        protocol: "http",
        hostname: "seoanalyser.com.au",
      },
      {
        protocol: "https",
        hostname: "cdn.simpleicons.org",
      },
    ],
    formats: ["image/webp", "image/avif"],
    minimumCacheTTL: 31536000, // 1 year for better caching
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    qualities: [75, 100], // Configure qualities to support quality={100}
  },

  // Performance optimizations for Next.js 15
  experimental: {
    optimizePackageImports: [
      "framer-motion",
      "react-icons",
      "recharts",
      "@tanstack/react-query",
      "react-apexcharts",
      "react-hook-form",
      "zustand",
    ],
    // Next.js 15 specific optimizations
    serverComponentsHmrCache: false, // Disable in production
    // Static generation optimizations
    staticGenerationMaxConcurrency: 8,
    staticGenerationMinPagesPerWorker: 25,
  },

  // Bundle optimization (stable in Next.js 15)
  bundlePagesRouterDependencies: true,

  // Server external packages optimization
  // Removed sharp from external packages to fix Windows compatibility
  serverExternalPackages: [],

  // Turbopack configuration (stable in Next.js 15+)
  turbopack: {
    rules: {
      "*.svg": {
        loaders: ["@svgr/webpack"],
        as: "*.js",
      },
    },
  },

  // Compression and security
  compress: true,
  poweredByHeader: false,
  // Enhanced headers for performance and security
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          // CORS headers
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS",
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "X-Requested-With, Content-Type, Authorization, X-Device-ID",
          },
          // Performance headers
          {
            key: "X-DNS-Prefetch-Control",
            value: "on",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          // Clickjacking protection via CSP (modern browsers)
          {
            key: "Content-Security-Policy",
            value: "frame-ancestors 'none'",
          },
          // Performance optimization headers
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "Permissions-Policy",
            value: "camera=(), microphone=(), geolocation=()",
          },
        ],
      },
      // Static assets caching with enhanced performance
      {
        source: "/images/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
        ],
      },
      // Blog images caching
      {
        source: "/blog/:path*",
        headers: [
          // Ensure HTML blog pages are not served stale by browsers; keep 10min max-age
          {
            key: "Cache-Control",
            value: "no-cache, max-age=600, must-revalidate",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
        ],
      },
      {
        source: "/blog",
        headers: [
          // Ensure HTML blog pages are not served stale by browsers; keep 10min max-age
          {
            key: "Cache-Control",
            value: "no-cache, max-age=600, must-revalidate",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
        ],
      },
      {
        source: "/fonts/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
        ],
      },
      // JavaScript and CSS caching
      {
        source: "/_next/static/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      // API routes caching
      {
        source: "/api/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "no-store, max-age=0",
          },
        ],
      },
    ];
  },
};

export default withBundleAnalyzer(nextConfig);
